package com.snct.netty;

import com.alibaba.fastjson.JSONObject;

import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.dctcore.commoncore.domain.transfer.TransferPackage;
import com.snct.dctcore.commoncore.enums.PackageTypeEnum;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 数据补发服务类
 * 主要功能：
 * 1. 处理设备数据包的补发请求
 * 2. 处理大数据包拆分后的子包补发
 * 3. 管理数据传输过程中的状态信息
 * 4. 维护补发队列和重复发送控制
 */
@Service
public class RepairService {
    protected Logger logger = LoggerFactory.getLogger(RepairService.class);

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 补发设备数据
     * 根据命令编号列表，从Redis中查找对应的数据包并加入补发队列
     *
     * @param commandNumList 需要补发的命令编号列表
     */
    public void repairData(List<Integer> commandNumList) {
        // 参数校验
        if (commandNumList == null) {
            return;
        }

        // 获取Redis操作模板
        SetOperations<String, Integer> opsForSet = redisTemplate.opsForSet();
        ListOperations<String, TransferPackage> opsForList = redisTemplate.opsForList();

        // 补发数据使用优先级30的队列
        String listKey = RedisParameter.SHIP_DO_SEND_LIST_COST + 30;

        // 如果发送队列为空，清理补发标记集合，避免重复补发
        if (!redisTemplate.hasKey(listKey).booleanValue() || opsForList.size(listKey).longValue() == 0) {
            cleanSet(opsForSet, RedisParameter.SHIP_REPAIR_DATA_SET);
        }

        // 存储需要补发的数据包列表
        List<TransferPackage> repairList = Lists.newArrayList();

        // 遍历需要补发的命令编号
        for (Integer commandNum : commandNumList) {
            // 检查是否已经在补发集合中，避免重复补发
            if (Objects.requireNonNull(opsForSet.isMember(RedisParameter.SHIP_REPAIR_DATA_SET, commandNum))) {
                logger.info("UDP补数据222,set已经存在，--{}", commandNum);
            } else {
                // 将命令编号添加到补发标记集合
                opsForSet.add(RedisParameter.SHIP_REPAIR_DATA_SET, new Integer[]{commandNum});

                // 从Redis中查找原始数据包
                ValueOperations<String, TransferPackage> opsForValue = redisTemplate.opsForValue();
                String key = RedisParameter.SHIP_DEVICE_HASH_DATA + commandNum;
                TransferPackage transferPackage = opsForValue.get(key);

                // 如果原始数据不存在，创建一个默认的补发包
                if (transferPackage == null) {
                    logger.info("UDP补数据333,数据并不存在，--{}", commandNum);
                    transferPackage = new TransferPackage(
                        Long.valueOf(commandNum.intValue()),    // 命令编号
                        Long.valueOf(System.currentTimeMillis()), // 当前时间戳
                        PackageTypeEnum.DEVICE_DATA.getValue(), // 设备数据类型
                        1,                                      // 标记为补发
                        "a",                                    // 默认设备编码
                        -1,                                     // 默认设备类型
                        "zzzz"                                  // 默认消息内容
                    );
                }

                // 设置补发标志
                transferPackage.setIsRepair(1);
                repairList.add(transferPackage);
            }
        }

        // 将所有需要补发的数据包加入发送队列
        for (TransferPackage transferPackage2 : repairList) {
            opsForList.leftPush(listKey, transferPackage2);
            logger.info("UDP补数据444,数据加入队列，--{}", JSONObject.toJSONString(transferPackage2));
        }
    }

    /**
     * 补发拆包数据
     * 处理大数据包拆分后的子包补发请求
     *
     * 消息格式：commandNum,channelCode,status,packageNum1,packageNum2,...
     * 如果包含"SUCCESS"表示传输成功，需要清理传输状态
     *
     * @param repairMessage 补发消息，包含命令编号、通道编码和需要补发的包序号
     */
    public void repairPackage(String repairMessage) {
        // 解析补发消息，按逗号分割
        String[] strArr = repairMessage.split(",");
        String commandNum = strArr[0];    // 命令编号
        String channelCode = strArr[1];   // 通道编码

        // 如果消息包含SUCCESS，表示传输成功，清理传输状态
        if (strArr.length >= 2 && repairMessage.contains("SUCCESS")) {
            removeAndTranNew(commandNum, channelCode);
            return;
        }

        // 消息格式不正确，至少需要4个字段：commandNum,channelCode,status,packageNum
        if (strArr.length < 4) {
            return;
        }

        // 获取Redis操作模板
        ValueOperations<String, TransferPackage> opsForValue = redisTemplate.opsForValue();
        ListOperations<String, TransferPackage> opsForList = redisTemplate.opsForList();
        SetOperations<String, Integer> opsForSet = redisTemplate.opsForSet();

        // 拆包补发使用优先级40的队列
        String listKey = RedisParameter.SHIP_DO_SEND_LIST_COST + 40;

        // 从第4个字段开始是需要补发的包序号列表
        for (int i = 3; i < strArr.length; i++) {
            if (!StringUtils.isBlank(strArr[i])) {
                // 构造该通道的补发标记集合键名
                String repairSetKey = RedisParameter.SHIP_REPAIR_PACKAGE_DATA_SET + channelCode;

                // 检查该包序号是否已经在补发集合中
                if (opsForSet.isMember(repairSetKey, Integer.valueOf(strArr[i])).booleanValue()) {
                    logger.info("UDP补包222,set已经存在，--{}", strArr[i]);
                } else {
                    // 将包序号添加到补发标记集合
                    opsForSet.add(repairSetKey, new Integer[]{Integer.valueOf(strArr[i])});

                    // 构造拆包数据的Redis键名：commandNum_channelCode_packageNum
                    String key = RedisParameter.SHIP_PACKAGE_DATA + commandNum + "_" + channelCode + "_" + strArr[i];
                    TransferPackage transferPackage = (TransferPackage) opsForValue.get(key);

                    // 如果找到对应的拆包数据，加入补发队列
                    if (transferPackage != null) {
                        logger.info("UDP补包222,----{}", JSONObject.toJSONString(transferPackage));
                        transferPackage.setIsRepair(1);  // 标记为补发
                        opsForList.leftPush(listKey, transferPackage);
                    }
                }
            }
        }
    }

    /**
     * 清空Redis集合
     * 用于清理补发标记集合，避免重复补发
     *
     * @param opsForSet Redis集合操作对象
     * @param key 要清空的集合键名
     */
    private void cleanSet(SetOperations<String, Integer> opsForSet, String key) {
        // 检查键是否存在
        if (!redisTemplate.hasKey(key)) {
            return;
        }

        // 循环弹出集合中的所有元素直到集合为空
        while (opsForSet.size(key).longValue() > 0) {
            opsForSet.pop(key);
        }
    }

    /**
     * 移除传输标记并处理新的传输请求
     * 当数据传输成功时，清理该通道的传输状态标记
     *
     * @param commandNum 命令编号
     * @param channelCode 通道编码
     */
    private synchronized void removeAndTranNew(String commandNum, String channelCode) {
        try {
            // 获取当前正在传输的命令编号
            ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
            String transitNum = valueOperations.get(RedisParameter.SHIP_IN_TRANSIT_NUM + channelCode);

            if (StringUtils.isNotBlank(transitNum)) {
                // 只有当前命令编号大于等于正在传输的编号时才能移除标记
                // 防止旧的成功消息影响新的传输
                if (Long.parseLong(commandNum) < Long.parseLong(transitNum)) {
                    return;
                }
                logger.info("通道：{},移除成功,----{}", channelCode, transitNum);
                // 删除正在传输的标记
                redisTemplate.delete(RedisParameter.SHIP_IN_TRANSIT_NUM + channelCode);
            }

            // 检查是否有更新的数据包需要发送
            ValueOperations<String, Long> longValueOperations = redisTemplate.opsForValue();
            Long newestNum = longValueOperations.get(RedisParameter.SHIP_PACKAGE_NEWEST_NUM + channelCode);

            // 如果当前命令编号已经是最新的，说明该通道的数据已经发送完成
            if (newestNum != null && Long.parseLong(commandNum) >= newestNum) {
                logger.info("通道：{},已经发送过了，commandNum:{}", channelCode, commandNum);
            }
        } catch (Exception e) {
            logger.error("移除正在传输图片的标记失败---{}", e);
        }
    }

    /**
     * 移除传输信息标记
     * 强制清理指定通道的传输状态，通常在异常情况下使用
     *
     * @param channelCode 通道编码
     */
    public synchronized void removeTransitInfo(String channelCode) {
        logger.info("图片,移除正在传输图片的标记--,{}", channelCode);
        try {
            // 直接删除该通道的传输标记
            redisTemplate.delete(RedisParameter.SHIP_IN_TRANSIT_NUM + channelCode);
            logger.info("图片,移除成功");
        } catch (Exception e) {
            logger.error("移除正在传输图片的标记失败---{}", e);
        }
    }

    /**
     * 查询传输状态
     * 向接收端发送状态查询请求，确认数据传输情况
     *
     * @param channelCode 通道编码
     * @param commandNum 命令编号
     */
    synchronized void askTransitNumStatus(String channelCode, String commandNum) {
        ValueOperations<String, TransferPackage> opsForValue = redisTemplate.opsForValue();
        ListOperations<String, TransferPackage> opsForList = redisTemplate.opsForList();

        // 首先尝试获取第1个拆包数据作为模板
        String key = RedisParameter.SHIP_PACKAGE_DATA + commandNum + "_" + channelCode + "_1";
        TransferPackage transferPackage = opsForValue.get(key);

        // 如果第1个包不存在，尝试查找前5个包中的任意一个作为模板
        if (transferPackage == null) {
            for (int i = 0; i < 5; i++) {
                key = RedisParameter.SHIP_PACKAGE_DATA + commandNum + "_" + channelCode + "_" + i;
                transferPackage = opsForValue.get(key);
                if (transferPackage != null) {
                    break;
                }
            }

            // 如果找不到任何拆包数据，说明数据已过期或不存在
            if (transferPackage == null) {
                logger.error("transferPackage为空:{}", key);
                // 清理传输标记，允许新的传输
                removeTransitInfo(channelCode);
                return;
            }
        }

        // 设置为状态查询类型（isRepair=2表示状态查询）
        transferPackage.setIsRepair(2);
        // 将命令编号作为查询消息内容
        transferPackage.setMessage(commandNum);

        // 将状态查询包加入发送队列，发送3次确保接收端能收到
        String listKey = RedisParameter.SHIP_DO_SEND_LIST_COST + 40;
        opsForList.leftPush(listKey, transferPackage);
        opsForList.leftPush(listKey, transferPackage);
        opsForList.leftPush(listKey, transferPackage);
    }
}