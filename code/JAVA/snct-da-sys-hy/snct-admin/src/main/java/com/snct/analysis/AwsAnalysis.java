package com.snct.analysis;

import com.snct.analysis.vo.AwsHbaseVo1;
import com.snct.dctcore.commoncore.domain.hbase.AwsHbaseVo;
import com.snct.kafka.KafkaMessage;

import com.snct.common.utils.DateUtils;
import com.snct.system.domain.msg.aws.Wimwv;
import com.snct.system.domain.msg.aws.Wixdr;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.snct.analysis.GpsAnalysis.valuesTrim;


/**
 * class
 *
 * <AUTHOR>
 */
public class AwsAnalysis {

    protected static Logger logger = LoggerFactory.getLogger(AwsAnalysis.class);

    private static final String TEMP_KEY = "AWS_TEMP";

    private static Map<String, Wixdr> wixDrTempMap = new ConcurrentHashMap<>();

    // =====================新的=====================

    public static AwsHbaseVo getAwsList2(KafkaMessage kafkaMessage, Object object) {
        AwsHbaseVo awsHbaseVo;
        if (object == null) {
            awsHbaseVo = new AwsHbaseVo();
        } else {
            awsHbaseVo = (AwsHbaseVo) object;
        }
        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime().longValue();
            awsHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(Long.valueOf(currentTime)).toString());
            awsHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss").toString());
            translateLine(awsHbaseVo, kafkaMessage.getMsg(), kafkaMessage.getCode());
        } catch (Exception e) {
            logger.error("AWS解析出错,---{}", e);
        }
        return awsHbaseVo;
    }

    public static AwsHbaseVo getAwsNewList(KafkaMessage kafkaMessage) {
        if (StringUtils.isBlank(kafkaMessage.getMsg())) {
            return null;
        }
        AwsHbaseVo awsHbaseVo = new AwsHbaseVo();
        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime().longValue();

            String[] values = valuesTrim(kafkaMessage.getMsg().split(" ", -1));
            for (int j = 0; j < values.length; j++) {
                if (values[j].startsWith("V")) {
                    awsHbaseVo.setRelativeWindSpeed(values[j].substring(1, values[j].length()));
                } else if (values[j].startsWith("D")) {
                    awsHbaseVo.setRelativeWind(values[j].substring(1, values[j].length()));
                } else if (values[j].startsWith("T")) {
                    awsHbaseVo.setAirTemperature(values[j].substring(1, values[j].length()));
                } else if (values[j].startsWith("H")) {
                    awsHbaseVo.setHumidity(values[j].substring(1, values[j].length()));
                }
            }

            awsHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(Long.valueOf(currentTime)).toString());
            awsHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss").toString());
        } catch (Exception e) {
            logger.error("AWS解析出错,---{}", e);
        }
        return awsHbaseVo;
    }

    private static void translateLine(AwsHbaseVo1 awsHbaseVo, String line, String code) {
        String prefix = line.substring(0, line.indexOf(","));
        switch (prefix) {
            case "$WIMWV":
                Wimwv wimwv = new Wimwv();
                wimwv.dataAnalysis(line);
                if (!org.springframework.util.StringUtils.isEmpty(wimwv.getRelativeWind())) {
                    awsHbaseVo.setWindLogoR(wimwv.getWindLogoR());
                    awsHbaseVo.setRelativeWind(wimwv.getRelativeWind());
                    awsHbaseVo.setRelativeWindSpeed(wimwv.getRelativeWindSpeed());
                }
                if (!org.springframework.util.StringUtils.isEmpty(wimwv.getTrueWind())) {
                    awsHbaseVo.setWindLogoT(wimwv.getWindLogoT());
                    awsHbaseVo.setTrueWind(wimwv.getTrueWind());
                    awsHbaseVo.setTrueWindSpeed(wimwv.getTrueWindSpeed());
                }
                if (!org.springframework.util.StringUtils.isEmpty(wimwv.getWindSpeedUnit())) {
                    awsHbaseVo.setWindSpeedUnit(wimwv.getWindSpeedUnit());
                    break;
                }
                break;
            case "$WIXDR":
                Wixdr wixdr = new Wixdr();
                wixdr.dataAnalysis(line);
                BeanUtils.copyProperties(wixdr, awsHbaseVo);
                break;
        }
    }


    // =====================旧的=====================
    /**
     * 从一组aws数据中，取得有用的字段
     *
     * @param kafkaMessage
     * @return
     */
    public static AwsHbaseVo getAwsList(KafkaMessage kafkaMessage) {
        AwsHbaseVo awsHbaseVo = null;

        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
        if (temp == null) {
            temp = new ArrayList<>();
        }

        try {
            if (kafkaMessage.getMsg().startsWith("$WIMWV") && temp.size() >= 2) {
                awsHbaseVo = new AwsHbaseVo();
                long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() :
                        kafkaMessage.getInitialTime();
                awsHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());

                for (String line : temp) {
                    translateLine(awsHbaseVo, line, kafkaMessage.getCode());
                }

                temp.clear();
            }

            // 类型为1时必须是第一条保存，
            int type = getLineType(kafkaMessage.getMsg());
            boolean b = type == 1 || (type == 2 && temp.size() != 1) || (type == 3 && temp.size() != 2);
            if (b) {
                temp.clear();
            }
            b = type == 1 || (type == 2 && temp.size() == 1) || (type == 3 && temp.size() == 2);
            if (b) {
                temp.add(kafkaMessage.getMsg());
                BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, null);
            }

        } catch (Exception e) {
            logger.error("aws解析出错,---{}", e);
            temp.clear();
        }
        return awsHbaseVo;
    }

    /**
     * 解析预览
     *
     * @param kafkaMessage
     * @return
     */
    public static AwsHbaseVo getParseAwsList(List<KafkaMessage> kafkaMessage) {
        AwsHbaseVo awsHbaseVo = null;

        List<String> temp = new ArrayList<>();
        for (KafkaMessage kafkaMessage1 :
                kafkaMessage) {
            if (kafkaMessage1.getMsg().startsWith("$WIMWV")) {
                temp.add(kafkaMessage1.getMsg());
            }


        }
        try {
            awsHbaseVo = new AwsHbaseVo();

            for (String line : temp) {
                translateLine(awsHbaseVo, line, kafkaMessage.get(0).getCode());
            }
            if (checkObjAllFieldsIsNull(awsHbaseVo)) {
                long currentTime = kafkaMessage.get(0).getInitialTime() == null ? System.currentTimeMillis() :
                        kafkaMessage.get(0).getInitialTime();
                awsHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime, "yyyy-MM-dd HH:mm:ss").toString());
            } else {
                return null;
            }
            temp.clear();

        } catch (Exception e) {
            logger.error("aws解析出错,---{}", e);
            temp.clear();
            return null;
        }
        return awsHbaseVo;
    }

    /**
     * 转换一行数据
     *
     * @param line
     * @return
     */
    private static void translateLine(AwsHbaseVo awsHbaseVo, String line, String code) {
        String prefix = line.substring(0, line.indexOf(","));
        switch (prefix) {
            //（全球地位信息）
            case "$WIMWV": {
                Wimwv wimwv = new Wimwv();
                wimwv.dataAnalysis(line);
                if (!StringUtils.isEmpty(wimwv.getRelativeWind())) {
                    awsHbaseVo.setWindLogoR(wimwv.getWindLogoR());
                    awsHbaseVo.setRelativeWind(wimwv.getRelativeWind());
                    awsHbaseVo.setRelativeWindSpeed(wimwv.getRelativeWindSpeed());
                }
                if (!StringUtils.isEmpty(wimwv.getTrueWind())) {
                    awsHbaseVo.setWindLogoT(wimwv.getWindLogoT());
                    awsHbaseVo.setTrueWind(wimwv.getTrueWind());
                    awsHbaseVo.setTrueWindSpeed(wimwv.getTrueWindSpeed());
                }
                awsHbaseVo.setWindSpeedUnit(wimwv.getWindSpeedUnit());
            }
            break;

            case "$WIXDR": {
                Wixdr wixdr = new Wixdr();
                wixdr.dataAnalysis(line);
                // 更新WIXDR的值
                wixDrTempMap.put(getWixDrTempMapKey(code), wixdr);
            }
            break;

            default: {
            }
        }

        // 为每秒的数据赋值
        if (wixDrTempMap.get(getWixDrTempMapKey(code)) != null) {
            Wixdr wixdr = wixDrTempMap.get(getWixDrTempMapKey(code));

            BeanUtils.copyProperties(wixdr, awsHbaseVo);
        }
    }

    /**
     * 判断是哪种类型的数据
     * $WIMWV  R 为1   $WIMWV  T为2  $WIXDR为3
     *
     * @param line
     * @return
     */
    private static int getLineType(String line) {
        String prefix = line.substring(0, line.indexOf(","));
        switch (prefix) {
            case "$WIMWV": {
                Wimwv wimwv = new Wimwv();
                wimwv.dataAnalysis(line);
                if (wimwv.getRelativeWind() != null) {
                    return 1;
                } else {
                    return 2;
                }
            }

            case "$WIXDR": {
                return 3;
            }

            default: {
                return 0;
            }
        }
    }

    private static String getWixDrTempMapKey(String code) {
        return "newWixDr_" + code;
    }

    /**
     * 判断对象中属性值是否全为空
     *
     * @param object
     * @return
     */
    public static boolean checkObjAllFieldsIsNull(Object object) {
        if (null == object) {
            return false;
        }
        List<Boolean> list = new ArrayList<>();
        try {
            for (Field f : object.getClass().getDeclaredFields()) {
                f.setAccessible(true);

                if (f.get(object) != null) {
                    //不为空
                    list.add(true);
                } else {
                    list.add(false);
                }

            }
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i) == true) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

}
