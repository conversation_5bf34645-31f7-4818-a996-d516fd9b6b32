package com.snct.analysis;

import com.snct.system.domain.BuDataPdu;
import com.snct.utils.HexUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PDU数据解析器
 * 类似AttitudeAnalysis的处理模式，统一解析PDU原始数据
 *
 * @author: wzewei
 * @date: 2025-09-05 11:15
 */
public class PduAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(PduAnalysis.class);

    /**
     * 统一解析PDU数据批次（类似AttitudeAnalysis.getAttitudeList）
     *
     * @param rawDataList 原始数据列表
     * @param deviceId 设备ID
     * @param deviceCode 设备编码
     * @param deviceName 设备名称
     * @return 解析后的PDU数据对象
     */
    public static BuDataPdu parsePduDataBatch(List<String> rawDataList, Long deviceId, String deviceCode, String deviceName) {
        if (rawDataList == null || rawDataList.isEmpty()) {
            logger.warn("PDU原始数据为空");
            return null;
        }

        BuDataPdu buDataPdu = null;

        try {
            // 创建PDU数据对象
            buDataPdu = new BuDataPdu();
            buDataPdu.setDeviceId(Math.toIntExact(deviceId));
            buDataPdu.setDeviceCode(deviceCode);
            buDataPdu.setDeviceName(deviceName);

            long timestamp = System.currentTimeMillis();
            buDataPdu.setInitialTime(timestamp);
            buDataPdu.setInitialBjTime(new Date(timestamp));

            // 用于存储解析的数据
            Map<String, Double> systemData = new HashMap<>();
            Map<Integer, ChannelData> channelDataMap = new HashMap<>();

            // 遍历所有原始数据，统一解析
            for (String hexData : rawDataList) {
                if (hexData == null || hexData.length() < 4) {
                    continue;
                }

                String command = hexData.substring(0, 4);

                switch (command) {
                    case "AA0C": // 通道数据
                        parseChannelData(hexData, channelDataMap);
                        break;
                    case "AA08": // 系统数据（电流/电压）
                        parseSystemData(hexData, systemData);
                        break;
                    case "AA0A": // 总电能
                        parseTotalEnergy(hexData, systemData);
                        break;
                    case "AA0E": // 总功率
                        parseTotalPower(hexData, systemData);
                        break;
                    default:
                        logger.debug("未知PDU命令类型: {}", command);
                }
            }

            // 设置系统数据
            setSystemDataToPdu(buDataPdu, systemData);

            // 设置通道数据
            setChannelDataToPdu(buDataPdu, channelDataMap);

            logger.info("PDU数据解析完成，设备: {}, 通道数: {}", deviceCode, channelDataMap.size());

        } catch (Exception e) {
            logger.error("PDU数据批量解析失败", e);
            return null;
        }

        return buDataPdu;
    }

    /**
     * 解析通道数据 (AA0C)
     */
    private static void parseChannelData(String hexData, Map<Integer, ChannelData> channelDataMap) {
        if (hexData.length() < 22) {
            logger.warn("通道数据格式错误，长度不足: {}", hexData);
            return;
        }

        try {
            // 获取通道号
            String channelStr = hexData.substring(10, 12);
            Integer channel = HexUtil.hexToInt(channelStr);

            // 获取电流
            String currentStr = hexData.substring(12, 16);
            Integer currentRaw = HexUtil.hexToInt(currentStr);
            double current = currentRaw / 10.0;

            // 获取功率
            String powerStr = hexData.substring(16, 20);
            Integer powerRaw = HexUtil.hexToInt(powerStr);
            double power = powerRaw / 10.0;

            // 获取状态
            String statusStr = hexData.substring(20, 22);
            Integer status = HexUtil.hexToInt(statusStr);

            // 存储通道数据
            ChannelData channelData = new ChannelData();
            channelData.electric = current;
            channelData.power = power;
            channelData.status = status.longValue();

            channelDataMap.put(channel, channelData);

            logger.debug("解析通道数据 - 通道: {}, 电流: {}A, 功率: {}W, 状态: {}",
                    channel, current, power, status);

        } catch (Exception e) {
            logger.error("解析通道数据失败: {}", hexData, e);
        }
    }

    /**
     * 解析系统数据 (AA08)
     */
    private static void parseSystemData(String hexData, Map<String, Double> systemData) {
        if (hexData.length() < 14) {
            logger.warn("系统数据格式错误，长度不足: {}", hexData);
            return;
        }

        try {
            // 获取消息类型
            String typeStr = hexData.substring(6, 8);
            // 获取数值
            String valueStr = hexData.substring(10, 14);
            Integer valueRaw = HexUtil.hexToInt(valueStr);
            double value = valueRaw / 10.0;

            if ("02".equals(typeStr)) {
                // 输入电流
                systemData.put("electric", value);
                logger.debug("解析系统电流: {}A", value);
            } else if ("03".equals(typeStr)) {
                // 输入电压
                systemData.put("voltage", value);
                logger.debug("解析系统电压: {}V", value);
            }

        } catch (Exception e) {
            logger.error("解析系统数据失败: {}", hexData, e);
        }
    }

    /**
     * 解析总电能数据 (AA0A)
     */
    private static void parseTotalEnergy(String hexData, Map<String, Double> systemData) {
        if (hexData.length() < 18) {
            logger.warn("总电能数据格式错误，长度不足: {}", hexData);
            return;
        }

        try {
            // 解析总电能值
            String valueStr = hexData.substring(10, 18);
            Integer valueRaw = HexUtil.hexToInt(valueStr);

            // 计算总电能实际值：总电能 = valueRaw / (2^16 * 10)
            double totalEnergy = valueRaw / (Math.pow(2, 16) * 10);

            systemData.put("manage", totalEnergy);

            logger.debug("解析总电能: {}kWh (原始值: {})", totalEnergy, valueRaw);

        } catch (Exception e) {
            logger.error("解析总电能数据失败: {}", hexData, e);
        }
    }

    /**
     * 解析总功率数据 (AA0E)
     */
    private static void parseTotalPower(String hexData, Map<String, Double> systemData) {
        if (hexData.length() < 22) {
            logger.warn("总功率数据格式错误，长度不足: {}", hexData);
            return;
        }

        try {
            // 获取有功功率
            String activePowerStr = hexData.substring(10, 14);
            Integer activePowerRaw = HexUtil.hexToInt(activePowerStr);
            double activePower = activePowerRaw / 10.0;
            systemData.put("yesPower", activePower);

            // 获取无功功率
            String reactivePowerStr = hexData.substring(14, 18);
            Integer reactivePowerRaw = HexUtil.hexToInt(reactivePowerStr);
            double reactivePower = reactivePowerRaw / 10.0;
            systemData.put("noPower", reactivePower);

            // 获取视在功率
            String apparentPowerStr = hexData.substring(18, 22);
            Integer apparentPowerRaw = HexUtil.hexToInt(apparentPowerStr);
            double apparentPower = apparentPowerRaw / 10.0;
            systemData.put("seePower", apparentPower);

            // 获取功率因数
            if (hexData.length() >= 24) {
                String powerFactorStr = hexData.substring(22, 24);
                Integer powerFactor = HexUtil.hexToInt(powerFactorStr);
                systemData.put("powerParam", powerFactor.doubleValue());
            }

            logger.debug("解析总功率 - 有功: {}kW, 无功: {}kW, 视在: {}kW",
                    activePower, reactivePower, apparentPower);

        } catch (Exception e) {
            logger.error("解析总功率数据失败: {}", hexData, e);
        }
    }

    /**
     * 设置系统数据到PDU对象
     */
    private static void setSystemDataToPdu(BuDataPdu buDataPdu, Map<String, Double> systemData) {
        if (systemData.containsKey("electric")) {
            buDataPdu.setElectric(BigDecimal.valueOf(systemData.get("electric")));
        }
        if (systemData.containsKey("voltage")) {
            buDataPdu.setVoltage(BigDecimal.valueOf(systemData.get("voltage")));
        }
        if (systemData.containsKey("manage")) {
            buDataPdu.setManage(BigDecimal.valueOf(systemData.get("manage")));
        }
        if (systemData.containsKey("yesPower")) {
            buDataPdu.setYesPower(BigDecimal.valueOf(systemData.get("yesPower")));
        }
        if (systemData.containsKey("noPower")) {
            buDataPdu.setNoPower(BigDecimal.valueOf(systemData.get("noPower")));
        }
        if (systemData.containsKey("seePower")) {
            buDataPdu.setSeePower(BigDecimal.valueOf(systemData.get("seePower")));
        }
        if (systemData.containsKey("powerParam")) {
            buDataPdu.setPowerParam(systemData.get("powerParam").longValue());
        }
    }

    /**
     * 设置通道数据到PDU对象
     */
    private static void setChannelDataToPdu(BuDataPdu buDataPdu, Map<Integer, ChannelData> channelDataMap) {
        for (int i = 1; i <= 8; i++) {
            ChannelData channelData = channelDataMap.get(i);
            if (channelData != null) {
                setChannelDataByIndex(buDataPdu, i, channelData);
            }
        }
    }

    /**
     * 根据通道索引设置通道数据
     */
    private static void setChannelDataByIndex(BuDataPdu buDataPdu, int channelIndex, ChannelData channelData) {
        BigDecimal electric = BigDecimal.valueOf(channelData.electric);
        BigDecimal power = BigDecimal.valueOf(channelData.power);
        Long status = channelData.status;

        switch (channelIndex) {
            case 1:
                buDataPdu.setElectric1(electric);
                buDataPdu.setPower1(power);
                buDataPdu.setStatus1(status);
                break;
            case 2:
                buDataPdu.setElectric2(electric);
                buDataPdu.setPower2(power);
                buDataPdu.setStatus2(status);
                break;
            case 3:
                buDataPdu.setElectric3(electric);
                buDataPdu.setPower3(power);
                buDataPdu.setStatus3(status);
                break;
            case 4:
                buDataPdu.setElectric4(electric);
                buDataPdu.setPower4(power);
                buDataPdu.setStatus4(status);
                break;
            case 5:
                buDataPdu.setElectric5(electric);
                buDataPdu.setPower5(power);
                buDataPdu.setStatus5(status);
                break;
            case 6:
                buDataPdu.setElectric6(electric);
                buDataPdu.setPower6(power);
                buDataPdu.setStatus6(status);
                break;
            case 7:
                buDataPdu.setElectric7(electric);
                buDataPdu.setPower7(power);
                buDataPdu.setStatus7(status);
                break;
            case 8:
                buDataPdu.setElectric8(electric);
                buDataPdu.setPower8(power);
                buDataPdu.setStatus8(status);
                break;
        }
    }

    /**
     * 通道数据内部类
     */
    private static class ChannelData {
        double electric;
        double power;
        long status;
    }
}
