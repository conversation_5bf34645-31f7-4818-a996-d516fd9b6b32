package com.snct.serialport;

import com.alibaba.fastjson2.JSONObject;
import com.snct.common.config.SnctConfig;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.system.domain.SerialConfig;
import com.snct.system.domain.SysOperLog;
import com.snct.system.service.ISerialConfigService;
import com.snct.system.service.impl.SerialConfigServiceImpl;
import com.snct.system.service.impl.SysOperLogServiceImpl;
import com.snct.utils.SysCmd2;
import gnu.io.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * class
 *
 * <AUTHOR>
 */
@Component
public class SerialPortUtil {

    private static SysOperLogServiceImpl sysOperLogService = SpringUtils.getBean(SysOperLogServiceImpl.class);

    private static final Logger logger = LoggerFactory.getLogger(SerialPortUtil.class);

    /**
     * 服务器所有串口
     */
    private static Set<String> serialPortNameSets;

    private static Map<String, SerialPort> openPortMap = new ConcurrentHashMap<>();

    /**
     * 查找电脑上所有可用 com 端口
     *
     * @return 可用端口名称列表，没有时 列表为空
     */
    public static void findSystemAllSerialPort() {
        serialPortNameSets = new HashSet<>();
        Enumeration<CommPortIdentifier> portList = CommPortIdentifier.getPortIdentifiers();

        logger.info("===========串口信息start==============");
        while (portList.hasMoreElements()) {
            CommPortIdentifier commPortIdentifier = portList.nextElement();
            StringBuilder returnStr = new StringBuilder();
            String portName = commPortIdentifier.getName();
            returnStr.append("端口：").append(portName).append("---占用情况:").append(commPortIdentifier.isCurrentlyOwned());
            logger.info("---{}", returnStr.toString());
            serialPortNameSets.add(portName);
        }
        logger.info("===========串口信息end==============");
    }

    public static Set<String> getSerialPortNameSets() {
        return serialPortNameSets;
    }

    public static void addOpenPort(String portName, SerialPort serialPort) {
        if (serialPort == null) {
            return;
        }
        openPortMap.put(portName, serialPort);
    }

    /**
     * 删除已打开的串口
     */
    public static void delOpenPort(String portName) {
        if (openPortMap.get(portName) == null) {
            return;
        }
        closeComPort(openPortMap.get(portName));
        openPortMap.remove(portName);
    }

    /**
     * 删除已打开的串口
     */
    public static void delOpenPort(List<String> portNameList) {
        if (portNameList == null) {
            return;
        }
        for (String portName : portNameList) {
            delOpenPort(portName);
        }
    }

    /**
     * 获取已打开的串口
     */
    public static SerialPort getOpenPortByName(String portName) {
        return openPortMap.get(portName);
    }

//    /**
//     * 获取已打开的串口
//     */
//    public static SerialPort getOpenPortByName(String portName) {
//        String oldSerialPort = getOldSerialPort(portName);
//        return openPortMap.get(oldSerialPort);
//    }


    /**
     * 根据新的串口名称获取对应的旧串口名称
     *
     * @param newSerialPort 新的串口名称
     * @return 对应的旧串口名称，如果没有找到则返回null
     */
    public static String getOldSerialPort(String newSerialPort) {
        ISerialConfigService serialConfigService = SpringUtils.getBean(SerialConfigServiceImpl.class);
        if (StringUtils.isNotBlank(newSerialPort)) {
            SerialConfig se = new SerialConfig();
            se.setNewName(newSerialPort);
            SerialConfig serialConfig = serialConfigService.selectSerialConfig(se);
            if (serialConfig != null) {
                String oldSerialName = serialConfig.getOldName();
                return oldSerialName;
            }
            return null;
        }
        return null;
    }

    /**
     * 清除已打开的串口
     */
    public static void cleanOpenMap() {
        if (openPortMap.size() == 0) {
            return;
        }
        for (String key : openPortMap.keySet()) {
            closeComPort(openPortMap.get(key));
        }
        openPortMap.clear();
    }

    /**
     * 打开电脑上指定的串口
     *
     * @param portName 端口名称，如 COM1，为 null 时，默认使用电脑中能用的端口中的第一个
     * @param b        波特率(baudrate)，如 9600
     * @param d        数据位（datebits），如 SerialPort.DATABITS_8 = 8
     * @param s        停止位（stopbits），如 SerialPort.STOPBITS_1 = 1
     * @param p        校验位 (parity)，如 SerialPort.PARITY_NONE = 0
     * @return 打开的串口对象，打开失败时，返回 null
     */
    public static SerialPort openComPort(String portName, int b, int d, int s, int p) {
        CommPort commPort = null;
        try {
            logger.info("开始打开串口：portName={},baudrate={},datebits={},stopbits={},parity={}", portName, b, d, s, p);
            //通过端口名称识别指定 COM 端口
            CommPortIdentifier portIdentifier = CommPortIdentifier.getPortIdentifier(portName);
            /**
             * open(String TheOwner, int i)：打开端口
             * TheOwner 自定义一个端口名称，随便自定义即可
             * i：打开的端口的超时时间，单位毫秒，超时则抛出异常：PortInUseException if in use.
             * 如果此时串口已经被占用，则抛出异常：gnu.io.PortInUseException: Unknown Application
             */
            commPort = portIdentifier.open(portName, 5000);
            /**
             * 判断端口是不是串口
             * public abstract class SerialPort extends CommPort
             */
            if (commPort instanceof SerialPort) {
                SerialPort serialPort = (SerialPort) commPort;
                /**
                 * 设置串口参数：setSerialPortParams( int b, int d, int s, int p )
                 * b：波特率（baudrate）
                 * d：数据位（datebits），SerialPort 支持 5,6,7,8
                 * s：停止位（stopbits），SerialPort 支持 1,2,3
                 * p：校验位 (parity)，SerialPort 支持 0,1,2,3,4
                 * 如果参数设置错误，则抛出异常：gnu.io.UnsupportedCommOperationException: Invalid Parameter
                 * 此时必须关闭串口，否则下次 portIdentifier.open 时会打不开串口，因为已经被占用
                 */
                serialPort.setSerialPortParams(b, d, s, p);
                logger.info("打开串口: {} 成功...", portName);
//                SysOperLog sysOperLog = new SysOperLog();
//                sysOperLog.setTitle("打开串口:"+ portName+ "成功");
//                sysOperLogService.insertOperlog(sysOperLog);
                return serialPort;
            } else {
                logger.info("当前端口: {} 不是串口...", commPort.getName());
            }
        } catch (NoSuchPortException e) {
            e.printStackTrace();
        } catch (PortInUseException e) {
            logger.info("串口: {} 已经被占用，请先解除占用...", portName);
            e.printStackTrace();
        } catch (UnsupportedCommOperationException e) {
            logger.info("串口参数设置错误，关闭串口，数据位[5-8]、停止位[1-3]、验证位[0-4]...");
            e.printStackTrace();
            //此时必须关闭串口，否则下次 portIdentifier.open 时会打不开串口，因为已经被占用
            commPort.close();
        }
        logger.info("打开串口: {} 失败...", portName);
        SysOperLog sysOperLog = new SysOperLog();
        sysOperLog.setCostTime(0l);
        sysOperLog.setTitle("打开串口:" + portName + "失败");
        sysOperLogService.insertOperlog(sysOperLog);
        return null;
    }

    /**
     * 往串口发送数据
     *
     * @param serialPort 串口对象
     * @param bytes      待发送数据
     */
    public static void sendDataToComPort(SerialPort serialPort, byte[] bytes) {
        if (bytes == null || serialPort == null) {
            return;
        }
        logger.info("串口{}发送#######数据：", serialPort.getName());
        OutputStream outputStream = null;
        try {
            outputStream = serialPort.getOutputStream();
            outputStream.write(bytes);
            outputStream.flush();
            logger.debug("串口{}数据发送成功", serialPort.getName());
        } catch (IOException e) {
            logger.error("串口{}发送数据失败: {}", serialPort.getName(), e.getMessage());
            e.printStackTrace();
        }finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    logger.info("串口发送出错222---{}", e);
                    e.printStackTrace();
                }
            }
        }
    }

    public static void sendDataToComPortList(List<String> portNameList, byte[] bytes) {
        if (portNameList == null) {
            return;
        }
        for (String portName : portNameList) {
            sendDataToComPort(getOpenPortByName(portName), bytes);
        }
    }
    /**
     * 通过系统命令往串口写数据
     * 执行: printf 'data' > /dev/ttyS3
     *
     * @param data 要发送的数据内容
     * @param devicePath 串口设备路径，如 /dev/ttyS3
     * @return 执行结果，true表示成功，false表示失败
     */
    public static boolean sendDataByCommand(String data, String devicePath) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(devicePath)) {
            logger.error("发送数据失败：数据内容或设备路径为空");
            return false;
        }

        try {

            File file = new File(SnctConfig.getDevUrl() +"/send.sh");
            if (file.exists()) {
                file.delete();
            }

            //String data = "这是要保存到文件中的字符串";
            try (FileWriter writer = new FileWriter(SnctConfig.getDevUrl() +"/send.sh")) {
                writer.write("printf '"+data+"\\r\\n' > "+ devicePath);
                //System.out.println("文件写入成功");
            } catch (IOException e) {
                e.printStackTrace();
            }
            Thread.sleep(5000);
            // 使用SysCmd2工具类执行命令
            SysCmd2 sysCmd = SpringUtils.getBean(SysCmd2.class);
            sysCmd.cmd( "chmod +x "+ SnctConfig.getDevUrl() +"/send.sh");
            sysCmd.cmd("sudo "+SnctConfig.getDevUrl() +"/send.sh");
            sysCmd.cmd("bash "+SnctConfig.getDevUrl() +"/send.sh");


//            // 构建命令：printf 'data' > devicePath
//            //String command = String.format("sh -c \"printf '%s\\r\\n' > %s\"", data, devicePath);
//            //logger.info("执行串口写入命令: {}", command);
//            String command = SnctConfig.getDevUrl() +"/send.sh " + devicePath + ' ' + data + "\\r\\n";
//            logger.info("执行串口写入命令: {}", command);
//
//            // 使用SysCmd2工具类执行命令
//            SysCmd2 sysCmd = SpringUtils.getBean(SysCmd2.class);
//            sysCmd.cmd(command);
//
//            logger.info("串口数据发送完成: {} -> {}", data, devicePath);
            return true;

        } catch (Exception e) {
            logger.error("串口数据发送异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通过系统命令往串口写数据（带换行符）
     * 类似于执行: printf 'data\r\n' > /dev/ttyS3
     *
     * @param data 要发送的数据内容
     * @param devicePath 串口设备路径，如 /dev/ttyS3
     * @return 执行结果，true表示成功，false表示失败
     */
    public static boolean sendDataByCommandWithNewline(String data, String devicePath) {
        return sendDataByCommand(data + "\\r\\n", devicePath);
    }

    /**
     * 从串口读取数据
     *
     * @param serialPort 要读取的串口
     * @return 读取的数据
     */
    public static byte[] readData(SerialPort serialPort, Integer wait) {
        InputStream is = null;
        byte[] bytes;
        byte[] allBytes = null;
        try {
            //获得串口的输入流
            is = serialPort.getInputStream();
            //获得数据长度
            int bufflenth = is.available();
            while (bufflenth != 0) {
                //初始化byte数组
                bytes = new byte[bufflenth];
                is.read(bytes);
                allBytes = ArrayUtils.addAll(allBytes, bytes);
                Thread.sleep(wait);
                bufflenth = is.available();
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return allBytes;
    }

    /**
     * 给串口设置监听
     *
     * @param serialPort
     * @param listener
     */
    public static void setListenerToSerialPort(SerialPort serialPort, SerialPortEventListener listener) {
        try {
            //给串口添加事件监听
            serialPort.addEventListener(listener);
        } catch (TooManyListenersException e) {
            e.printStackTrace();
        }
        //串口有数据监听
        serialPort.notifyOnDataAvailable(true);
        //中断事件监听
        serialPort.notifyOnBreakInterrupt(true);
        logger.info("监听成功----{}", serialPort.getName());
    }


    /**
     * 关闭串口
     *
     * @param serialPort 待关闭的串口对象
     */
    public static void closeComPort(SerialPort serialPort) {
        if (serialPort == null) {
            return;
        }
        try {
            serialPort.removeEventListener();
            serialPort.close();
            logger.info("关闭串口 :---{}", serialPort.getName());
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("关闭串口:" + serialPort.getName());
            sysOperLogService.insertOperlog(sysOperLog);
        } catch (Exception e) {
            logger.info("关闭串口错误:---{}", e);
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("关闭串口错误");
            sysOperLogService.insertOperlog(sysOperLog);
        }
    }

    /**
     * 关闭串口
     *
     * @param serialPortList 待关闭的串口对象
     */
    public static void closeComPortList(List<SerialPort> serialPortList) {
        if (serialPortList == null) {
            return;
        }
        for (SerialPort serialPort : serialPortList) {
            closeComPort(serialPort);
        }
    }

    /**
     * 16进制字符串转十进制字节数组
     * 这是常用的方法，如某些硬件的通信指令就是提供的16进制字符串，发送时需要转为字节数组再进行发送
     *
     * @param strSource 16进制字符串，如 "455A432F5600"，每两位对应字节数组中的一个10进制元素
     *                  默认会去除参数字符串中的空格，所以参数 "45 5A 43 2F 56 00" 也是可以的
     * @return 十进制字节数组, 如 [69, 90, 67, 47, 86, 0]
     */
    public static byte[] hexString2Bytes(String strSource) {
        if (strSource == null || "".equals(strSource.trim())) {
            logger.info("hexString2Bytes 参数为空，放弃转换.");
            return null;
        }
        strSource = strSource.replace(" ", "");
        int l = strSource.length() / 2;
        byte[] ret = new byte[l];
        for (int i = 0; i < l; i++) {
            ret[i] = Integer.valueOf(strSource.substring(i * 2, i * 2 + 2), 16).byteValue();
        }
        return ret;
    }


    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2)
            data[i / 2] =
                    (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
        return data;
    }

    public static void sendCmd(String cmd, SerialPort serialPort) {
        sendDataToComPort(serialPort, hexStringToByteArray(cmd));
    }
}
