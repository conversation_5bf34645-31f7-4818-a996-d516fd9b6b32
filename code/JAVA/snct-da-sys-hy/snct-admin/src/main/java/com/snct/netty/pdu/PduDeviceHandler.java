package com.snct.netty.pdu;

import com.alibaba.fastjson2.JSONObject;
import com.snct.analysis.PduAnalysis;
import com.snct.common.constant.CacheConstants;
import com.snct.common.core.redis.RedisCache;
import com.snct.common.enums.KafkaMsgType;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.device.domain.Modem;
import com.snct.device.domain.SocketEntity;
import com.snct.kafka.KafkaMessage;
import com.snct.kafka.KafkaService;
import com.snct.netty.DeviceHandler;
import com.snct.netty.DeviceMessageListener;
import com.snct.system.domain.BuDataPdu;
import com.snct.system.service.IBuDataPduService;
import com.snct.system.service.impl.BuDataPduServiceImpl;
import com.snct.utils.HexUtil;
import com.snct.system.domain.Device;
import com.snct.system.domain.msg.BuMsgPdu;
import com.snct.system.domain.msg.BuMsgPduOut;
import com.snct.web.controller.business.DeviceController;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.util.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * PDU设备消息处理器
 * 处理PDU设备的消息解析和发送
 */
public class PduDeviceHandler extends DeviceHandler {

    private static final Logger logger = LoggerFactory.getLogger(PduDeviceHandler.class);

    RedisCache redisCache = SpringUtils.getBean(RedisCache.class);

    SocketEntity socketEntity = null;

    private static final String PDU_DATA = CacheConstants.DEVICE_DATA_KEY + "pdu";

    // 消息监听器列表
    private final List<DeviceMessageListener> listeners = new ArrayList<>();

    // 当前通道
    private Channel channel;

    // 设备地址
    private String deviceAddress;

    // 设备对象
    private Device device;
    
    // 是否收集完所有数据
    private boolean dataCollectionComplete = false;
    
    // 收到的通道数据数量计数
    private int receivedChannelCount = 0;
    
    // 通道数据是否全部已收集
    private boolean allChannelsCollected = false;
    
    // 当前批次的通道输出数据缓存，用于合并发送
    private final List<BuMsgPduOut> channelOutputBuffer = new ArrayList<>();

    // 命令序列
    private final String[] queryCommands = {
            "5506010100010000", // 查询通道1
            "5506010100020000", // 查询通道2
            "5506010100030000", // 查询通道3
            "5506010100040000", // 查询通道4
            "5506010100050000", // 查询通道5
            "5506010100060000", // 查询通道6
            "5506010100070000", // 查询通道7
            "5506010100080000", // 查询通道8
            "5506010500000000", // 查询系统功率
            "5506010200000000", // 查询输入电流
            "5506010300000000", // 查询输入电压
            "5506010400000000"  // 查询总电能
    };
    
    // 需要采集的通道数量
    private static final int TOTAL_CHANNELS = 8;

    // PDU数据
    private BuMsgPdu pduData = new BuMsgPdu();

    // PDU输出通道数据
    private Map<Integer, BuMsgPduOut> pduOutMap = new HashMap<>();

    // Kafka服务
    private KafkaService kafkaService;
    
    // 是否已经发送了数据
    private boolean dataSent = false;

    // ========== 新增：原始数据收集器 ==========
    // 原始数据收集器
    private final List<String> rawDataCollection = new ArrayList<>();
    // 收集开始时间
    private long collectionStartTime = 0;
    // 收集超时时间（毫秒）
    private static final long COLLECTION_TIMEOUT = 3000;

    /**
     * 设置设备对象
     *
     */
    public void setDevice(Device device) {
        this.device = device;
        // 初始化PDU数据对象
        this.pduData.setDeviceId(device != null ? device.getId() : null);
    }

    /**
     * 获取设备对象
     *
     */
    public Device getDevice() {
        return this.device;
    }

    /**
     * 获取船舶SN号
     *
     */
    public String getSn() {
        return device != null ? device.getSn() : null;
    }

    /**
     * 设置Kafka服务
     *
     * @param kafkaService Kafka服务
     */
    public void setKafkaService(KafkaService kafkaService) {
        this.kafkaService = kafkaService;
    }

    /**
     * 添加消息监听器
     */
    public void addMessageListener(DeviceMessageListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * 获取PDU数据
     */
    public BuMsgPdu getPduData() {
        return this.pduData;
    }

    /**
     * 获取PDU输出通道数据
     */
    public Map<Integer, BuMsgPduOut> getPduOutData() {
        return this.pduOutMap;
    }

    /**
     * 移除消息监听器
     */
    public void removeMessageListener(DeviceMessageListener listener) {
        listeners.remove(listener);
    }


    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        this.channel = ctx.channel();
        this.deviceAddress = ((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress();
        logger.info("PDU设备通道已激活: {}", deviceAddress);
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.info("PDU设备通道已关闭: {}", deviceAddress);
        super.channelInactive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (!ctx.channel().isOpen()) {
            return;
        }

        // 消息已经被解码为byte[]
        if (!(msg instanceof byte[])) {
            logger.warn("接收到的消息不是byte[]类型: {}", msg.getClass().getName());
            return;
        }

        // 获取消息内容（字节数组）
        byte[] response = (byte[]) msg;

        // 打印原始消息
        String hexString = HexUtil.byteArrayToHex(response);

        // 将消息保存到内存中，用于预览
        try {
            // 获取设备编码
            String deviceCode = device != null ? device.getCode() : null;
            // 只有在设备有编码时才可能存放预览数据
            if (deviceCode != null && !deviceCode.isEmpty()) {
                if (DeviceController.viewKey != null && DeviceController.viewKey.containsKey(deviceCode)) {
                    // 获取当前时间并格式化
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                    String formattedTime = now.format(formatter);
                    // 构建key: 设备编码###时间
                    String key = deviceCode + "###" + formattedTime;
                    // 保存原始数据到viewValue
                    DeviceController.viewValue.put(key, hexString);
                }
            }
        } catch (Exception e) {
            logger.error("保存预览数据异常", e);
        }

        logger.debug("收到PDU消息: {} 来自: {}", hexString, deviceAddress);

        // 解析消息 (去掉空格)
        String hexData = hexString.replaceAll(" ", "").trim();
        if (hexData.length() < 4) {
            logger.warn("消息格式错误，长度不足: {}", hexData);
            return;
        }

        // 使用新的数据收集方式
        collectRawData(hexData);
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        logger.error("PDU设备通信异常: {}", cause.getMessage(), cause);
        ctx.close();
    }

    @Override
    public String getDeviceType() {
        return "PDU";
    }

    @Override
    public boolean sendQueryCommand(List<String> commands) {
        if (channel == null || !channel.isActive()) {
            logger.error("PDU设备未连接，无法发送查询命令");
            return false;
        }

        try {
            for (String command : commands){

                // 发送命令
                byte[] bytes = HexUtil.hexToByteArray(command);
                channel.writeAndFlush(bytes);

                logger.debug("发送PDU查询命令: {}", command);
            }
            return true;
        } catch (Exception e) {
            logger.error("发送PDU查询命令时发生异常", e);
            return false;
        }
    }

    // ====================发送查询命令=======================

    /**
     * 一次性发送所有查询命令，并为结果分配同一个批次号
     *
     * @param delayMs 命令间延时(毫秒)
     * @return 是否成功发送所有查询命令
     */
    public boolean sendAllQueryCommands(int delayMs) {
        if (channel == null || !channel.isActive()) {
            logger.error("PDU设备未连接，无法发送查询命令");
            return false;
        }

        try {
            // 重置数据收集状态，准备新一轮采集
            resetDataCollectionStatus();

            // 重置原始数据收集器
            rawDataCollection.clear();

            // 清除通道数据 - 不再单独设置批次号
            pduOutMap.clear();

            for (String command : queryCommands) {
                // 发送命令
                byte[] bytes = HexUtil.hexToByteArray(command);
                channel.writeAndFlush(bytes);
                logger.debug("发送PDU查询命令: {}", command);

                // 命令之间添加短暂延时
                if (delayMs > 0 && command != queryCommands[queryCommands.length - 1]) {
                    Thread.sleep(delayMs);
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("批量发送PDU查询命令时发生异常", e);
            return false;
        }
    }

    // ========== 新增：原始数据收集方法 ==========

    /**
     * 收集原始数据
     */
    private void collectRawData(String hexData) {
        // 如果是新的收集周期，重置收集器
        if (rawDataCollection.isEmpty()) {
            collectionStartTime = System.currentTimeMillis();
            logger.debug("开始新的PDU数据收集周期");
        }

        // 添加到收集器
        rawDataCollection.add(hexData);
        logger.debug("收集PDU原始数据: {}, 当前收集数量: {}", hexData, rawDataCollection.size());

        // 检查是否收集完成或超时
        if (isCollectionComplete() || isCollectionTimeout()) {
            // 调用统一解析方法
            processBatchData();
        }
    }

    /**
     * 处理批量数据
     */
    private void processBatchData() {
        try {
            logger.info("PDU数据收集完成，开始批量解析，数据包数量: {}", rawDataCollection.size());

            // 调用PduAnalysis统一解析方法
            BuDataPdu buDataPdu = PduAnalysis.parsePduDataBatch(
                new ArrayList<>(rawDataCollection),
                device.getId(),
                device.getCode(),
                device.getName()
            );

            if (buDataPdu != null) {
                // 保存数据到数据库
                savePduData(buDataPdu);
                logger.info("PDU数据解析并保存成功，设备: {}", device.getCode());
            } else {
                logger.warn("PDU数据解析失败，设备: {}", device.getCode());
            }

        } catch (Exception e) {
            logger.error("PDU批量数据处理失败", e);
        } finally {
            // 清空收集器，准备下一批次
            rawDataCollection.clear();
        }
    }

    /**
     * 检查收集是否完成
     */
    private boolean isCollectionComplete() {
        // 简单判断：收到预期数量的数据包（8个通道 + 4个系统数据）
        return rawDataCollection.size() >= 12;
    }

    /**
     * 检查收集是否超时
     */
    private boolean isCollectionTimeout() {
        return System.currentTimeMillis() - collectionStartTime > COLLECTION_TIMEOUT;
    }

    /**
     * 保存PDU数据到数据库
     */
    private void savePduData(BuDataPdu buDataPdu) {
        try {
            IBuDataPduService buDataPduService = SpringUtils.getBean(BuDataPduServiceImpl.class);
            buDataPduService.insertBuDataPdu(buDataPdu);
            logger.debug("PDU数据保存到数据库成功");
        } catch (Exception e) {
            logger.error("保存PDU数据到数据库失败", e);
        }
    }


    // =======================消息解析=======================

//    /**
//     * 解析不同类型的PDU消息
//     */
//    private void parseMessage(String hexString) {
//        // 检查消息前缀
//        String prefix = hexString.substring(0, 2);
//
//        // 根据不同的消息类型进行解析
//        try {
//            // 判断是AA开头
//            if (prefix.equals("AA")) {
//                // AA开头协议
//                parseProtocolAAMessage(hexString);
//            } else {
//                logger.info("未知消息前缀: {}", prefix);
//                notifyListeners(prefix, hexString, null);
//            }
//        } catch (Exception e) {
//            logger.error("解析消息时发生异常: {}", hexString, e);
//        }
//    }

    /**
     * 解析AA开头的协议消息
     */
    private void parseProtocolAAMessage(String hexString) {
        if (hexString.length() < 4) {
            logger.warn("协议消息格式错误，长度不足: {}", hexString);
            return;
        }

        String command = hexString.substring(0, 4);

        // 初始化或获取SocketEntity对象
        if (StringUtils.isNotBlank(hexString)) {
            if (redisCache.getCacheObject(PDU_DATA) == null) {
                socketEntity = new SocketEntity();
            } else {
                socketEntity = redisCache.getCacheObject(PDU_DATA);
            }
        }

        switch (command) {
            case "AA0C":
                parsePowerChannelMessage(hexString);
                redisCache.setCacheObject(PDU_DATA,socketEntity);
                break;
            case "AA08":
                parseSystemMessage(hexString);
                redisCache.setCacheObject(PDU_DATA,socketEntity);
                break;
            case "AA0A":
                parseTotalEnergyMessage(hexString);
                redisCache.setCacheObject(PDU_DATA,socketEntity);
                break;
            case "AA0E":
                parseTotalPowerMessage(hexString);
                redisCache.setCacheObject(PDU_DATA,socketEntity);
                break;
            default:
                logger.info("未知消息类型: {}", command);
                notifyListeners(command, hexString, null);
        }
    }

    /**
     * 解析电源通道消息 (AA0C)
     */
    private void parsePowerChannelMessage(String hexString) {
        if (hexString.length() < 22) {
            logger.warn("电源通道消息格式错误，长度不足: {}", hexString);
            return;
        }

        try {
            // 获取通道号
            String channelStr = hexString.substring(10, 12);
            Integer channel = HexUtil.hexToInt(channelStr);

            // 获取电流
            String currentStr = hexString.substring(12, 16);
            Integer currentRaw = HexUtil.hexToInt(currentStr);
            double current = currentRaw / 10.0;

            // 获取功率
            String powerStr = hexString.substring(16, 20);
            Integer powerRaw = HexUtil.hexToInt(powerStr);
            double power = powerRaw / 10.0;

            // 获取状态
            String statusStr = hexString.substring(20, 22);
            Integer status = HexUtil.hexToInt(statusStr);

            logger.debug("电源通道信息 - 通道: {}, 电流: {}A, 功率: {}W, 状态: {}",
                    channel, current, power, status);

            // 更新PDU输出通道数据
            updatePduOutData(channel.toString(), currentRaw.toString(), powerRaw.toString(), status.toString());
            updatePduOutData(channel, current, power, status);

            // 通知监听器
            notifyListeners("AA0C", hexString, pduOutMap.get(channel));
            
            // 增加已收到的通道数量计数
            receivedChannelCount++;
            
            // 将通道数据加入缓存，等待合并发送
            BuMsgPduOut pduOut = pduOutMap.get(channel);
            if (pduOut != null && !channelOutputBuffer.contains(pduOut)) {
                channelOutputBuffer.add(pduOut);
            }
        } catch (Exception e) {
            logger.error("解析电源通道消息失败: {}", hexString, e);
        }
    }

    /**
     * 更新PDU输出通道数据
     */
    private void updatePduOutData(Integer channel, double current, double power, Integer status) {
        BuMsgPduOut pduOut = pduOutMap.computeIfAbsent(channel, k -> new BuMsgPduOut());
        pduOut.setDeviceId(device.getId());
        pduOut.setOutIndex(channel.longValue());
        pduOut.setElectric(current);
        pduOut.setPower(power);
        pduOut.setOutStatus(status.longValue());
    }

    /**
     * 更新PDU输出通道数据(SocketEntity)
     */
    private void updatePduOutData(String channel, String electric, String power, String status) {

        switch ( channel){
            case "1":
                socketEntity.setElectric1(electric);
                socketEntity.setPower1(power);
                socketEntity.setStatus1(status);
                break;
            case "2":
                socketEntity.setElectric2(electric);
                socketEntity.setPower2(power);
                socketEntity.setStatus2(status);
                break;
            case "3":
                socketEntity.setElectric3(electric);
                socketEntity.setPower3(power);
                socketEntity.setStatus3(status);
                break;
            case "4":
                socketEntity.setElectric4(electric);
                socketEntity.setPower4(power);
                socketEntity.setStatus4(status);
                break;
            case "5":
                socketEntity.setElectric5(electric);
                socketEntity.setPower5(power);
                socketEntity.setStatus5(status);
                break;
            case "6":
                socketEntity.setElectric6(electric);
                socketEntity.setPower6(power);
                socketEntity.setStatus6(status);
                break;
            case "7":
                socketEntity.setElectric7(electric);
                socketEntity.setPower7(power);
                socketEntity.setStatus7(status);
                break;
            case "8":
                socketEntity.setElectric8(electric);
                socketEntity.setPower8(power);
                socketEntity.setStatus8(status);
                break;
        }
    }


    /**
     * 解析系统消息 (AA08)
     * 可能是电流或电压数据
     */
    private void parseSystemMessage(String hexString) {
        if (hexString.length() < 14) {
            logger.warn("系统消息格式错误，长度不足: {}", hexString);
            return;
        }

        try {
            // 获取消息类型
            String typeStr = hexString.substring(6, 8);
            // 获取数值
            String valueStr = hexString.substring(10, 14);
            Integer valueRaw = HexUtil.hexToInt(valueStr);
            double value = valueRaw / 10.0;

            if ("02".equals(typeStr)) {
                // 输入电流
                logger.info("系统电流信息 - 电流: {}A", value);
                socketEntity.setElectric(valueRaw.toString());
                // 更新PDU总数据
                pduData.setElectric(value);
            } else if ("03".equals(typeStr)) {
                // 输入电压
                logger.info("系统电压信息 - 电压: {}V", value);
                socketEntity.setVoltage(valueRaw.toString());
                // 更新PDU总数据
                pduData.setVoltage(value);
            } else {
                logger.info("未知系统信息类型: {}, 值: {}", typeStr, value);
            }

            // 通知监听器
            notifyListeners("AA08", hexString, pduData);
        } catch (Exception e) {
            logger.error("解析系统消息失败: {}", hexString, e);
        }
    }

    /**
     * 解析总电能消息 (AA0A)
     */
    private void parseTotalEnergyMessage(String hexString) {
        if (hexString.length() < 18) {
            logger.warn("总电能消息格式错误，长度不足: {}", hexString);
            return;
        }

        try {
            // 解析总电能值
            String valueStr = hexString.substring(10, 18);
            Integer valueRaw = HexUtil.hexToInt(valueStr);

            socketEntity.setManage(valueRaw.toString());
            // 计算总电能实际值：总电能 = valueRaw / (2^16 * 10)
            double totalEnergy = valueRaw / (Math.pow(2, 16) * 10);

            logger.info("总电能信息 - 总电能: {}kWh (原始值: {})", totalEnergy, valueRaw);

            // 更新PDU总数据
            pduData.setManage(totalEnergy);

            // 通知监听器
            notifyListeners("AA0A", hexString, pduData);
        } catch (Exception e) {
            logger.error("解析总电能消息失败: {}", hexString, e);
        }
    }

    /**
     * 解析总功率消息 (AA0E)
     */
    private void parseTotalPowerMessage(String hexString) {
        if (hexString.length() < 22) {
            logger.warn("总功率消息格式错误，长度不足: {}", hexString);
            return;
        }

        try {
            // 获取有功功率
            String activePowerStr = hexString.substring(10, 14);
            Integer activePowerRaw = HexUtil.hexToInt(activePowerStr);
            double activePower = activePowerRaw / 10.0;
            socketEntity.setYesPwoer(activePowerRaw.toString());

            // 获取无功功率
            String reactivePowerStr = hexString.substring(14, 18);
            Integer reactivePowerRaw = HexUtil.hexToInt(reactivePowerStr);
            double reactivePower = reactivePowerRaw / 10.0;
            socketEntity.setNoPwoer(reactivePowerRaw.toString());

            // 获取视在功率
            String apparentPowerStr = hexString.substring(18, 22);
            Integer apparentPowerRaw = HexUtil.hexToInt(apparentPowerStr);
            double apparentPower = apparentPowerRaw / 10.0;
            socketEntity.setSeePwoer(apparentPowerRaw.toString());

            // 获取功率因数
            String powerFactorStr = hexString.substring(22, 24);
            Integer powerFactor = HexUtil.hexToInt(powerFactorStr);
            socketEntity.setPowerParam(powerFactor.toString());

            logger.info("总功率信息 - 有功功率: {}kW, 无功功率: {}kW, 视在功率: {}kW, 功率因数: {}",
                    activePower, reactivePower, apparentPower, powerFactor);

            // 更新PDU总数据
            pduData.setDeviceId(device.getId());
            pduData.setYesPwoer(activePower);
            pduData.setNoPwoer(reactivePower);
            pduData.setSeePwoer(apparentPower);
            pduData.setPowerParam(powerFactor.longValue());

            // 通知监听器
            notifyListeners("AA0E", hexString, pduData);
        } catch (Exception e) {
            logger.error("解析总功率消息失败: {}", hexString, e);
        }
    }

    // =====================消息发送===========================

    /**
     * 检查是否已收集到所有所需数据
     * 如果已收集完毕，则一次性发送所有数据到Kafka
     */
    private void checkDataCollectionStatus() {
        // 如果数据已发送，无需再次检查和发送
        if (dataSent) {
            return;
        }

        // 检查是否已收集到所有通道数据 (8个通道)
        if (receivedChannelCount >= TOTAL_CHANNELS && !allChannelsCollected) {
            allChannelsCollected = true;
        }

        // 检查PDU设备数据是否完整 (电压、电流、总电能、功率)
        boolean pduDataComplete = pduData != null &&
                pduData.getVoltage() != null &&
                pduData.getElectric() != null &&
                pduData.getManage() != null &&
                pduData.getYesPwoer() != null;

        // 数据均收集完毕，发送到Kafka
        if (allChannelsCollected && pduDataComplete && !dataCollectionComplete) {
            dataCollectionComplete = true;
            //sendCombinedDataToKafka();
            // 保存数据到数据库
            BuDataPdu buDataPdu = new BuDataPdu();
            buDataPdu.setDeviceId(Math.toIntExact(device.getId()));
            buDataPdu.setDeviceCode(device.getCode());
            buDataPdu.setDeviceName(device.getName());
            long timestamp = System.currentTimeMillis();
            buDataPdu.setInitialTime(timestamp);
            buDataPdu.setInitialBjTime(new Date(timestamp));

            if (pduData.getElectric() != null) {
                buDataPdu.setElectric(BigDecimal.valueOf(pduData.getElectric()));
            }
            if (pduData.getManage() != null) {
                buDataPdu.setManage(BigDecimal.valueOf(pduData.getManage()));
            }
            if (pduData.getVoltage() != null) {
                buDataPdu.setVoltage(BigDecimal.valueOf(pduData.getVoltage()));
            }
            if (pduData.getYesPwoer() != null) {
                buDataPdu.setYesPower(BigDecimal.valueOf(pduData.getYesPwoer()));
            }
            if (pduData.getNoPwoer() != null) {
                buDataPdu.setNoPower(BigDecimal.valueOf(pduData.getNoPwoer()));
            }
            if (pduData.getSeePwoer() != null) {
                buDataPdu.setSeePower(BigDecimal.valueOf(pduData.getSeePwoer()));
            }
            if (pduData.getPowerParam() != null) {
                buDataPdu.setPowerParam(pduData.getPowerParam());
            }
            // 通道1
            if (pduOutMap.get(1) != null) {
                if (pduOutMap.get(1).getElectric() != null) {
                    buDataPdu.setElectric1(BigDecimal.valueOf(pduOutMap.get(1).getElectric()));
                }
                if (pduOutMap.get(1).getPower() != null) {
                    buDataPdu.setPower1(BigDecimal.valueOf(pduOutMap.get(1).getPower()));
                }
                if (pduOutMap.get(1).getOutStatus() != null) {
                    buDataPdu.setStatus1(pduOutMap.get(1).getOutStatus());
                }
            }

            // 通道2
            if (pduOutMap.get(2) != null) {
                if (pduOutMap.get(2).getElectric() != null) {
                    buDataPdu.setElectric2(BigDecimal.valueOf(pduOutMap.get(2).getElectric()));
                }
                if (pduOutMap.get(2).getPower() != null) {
                    buDataPdu.setPower2(BigDecimal.valueOf(pduOutMap.get(2).getPower()));
                }
                if (pduOutMap.get(2).getOutStatus() != null) {
                    buDataPdu.setStatus2(pduOutMap.get(2).getOutStatus());
                }
            }

            // 通道3
            if (pduOutMap.get(3) != null) {
                if (pduOutMap.get(3).getElectric() != null) {
                    buDataPdu.setElectric3(BigDecimal.valueOf(pduOutMap.get(3).getElectric()));
                }
                if (pduOutMap.get(3).getPower() != null) {
                    buDataPdu.setPower3(BigDecimal.valueOf(pduOutMap.get(3).getPower()));
                }
                if (pduOutMap.get(3).getOutStatus() != null) {
                    buDataPdu.setStatus3(pduOutMap.get(3).getOutStatus());
                }
            }

            // 通道4
            if (pduOutMap.get(4) != null) {
                if (pduOutMap.get(4).getElectric() != null) {
                    buDataPdu.setElectric4(BigDecimal.valueOf(pduOutMap.get(4).getElectric()));
                }
                if (pduOutMap.get(4).getPower() != null) {
                    buDataPdu.setPower4(BigDecimal.valueOf(pduOutMap.get(4).getPower()));
                }
                if (pduOutMap.get(4).getOutStatus() != null) {
                    buDataPdu.setStatus4(pduOutMap.get(4).getOutStatus());
                }
            }

            // 通道5
            if (pduOutMap.get(5) != null) {
                if (pduOutMap.get(5).getElectric() != null) {
                    buDataPdu.setElectric5(BigDecimal.valueOf(pduOutMap.get(5).getElectric()));
                }
                if (pduOutMap.get(5).getPower() != null) {
                    buDataPdu.setPower5(BigDecimal.valueOf(pduOutMap.get(5).getPower()));
                }
                if (pduOutMap.get(5).getOutStatus() != null) {
                    buDataPdu.setStatus5(pduOutMap.get(5).getOutStatus());
                }
            }

            // 通道6
            if (pduOutMap.get(6) != null) {
                if (pduOutMap.get(6).getElectric() != null) {
                    buDataPdu.setElectric6(BigDecimal.valueOf(pduOutMap.get(6).getElectric()));
                }
                if (pduOutMap.get(6).getPower() != null) {
                    buDataPdu.setPower6(BigDecimal.valueOf(pduOutMap.get(6).getPower()));
                }
                if (pduOutMap.get(6).getOutStatus() != null) {
                    buDataPdu.setStatus6(pduOutMap.get(6).getOutStatus());
                }
            }

            // 通道7
            if (pduOutMap.get(7) != null) {
                if (pduOutMap.get(7).getElectric() != null) {
                    buDataPdu.setElectric7(BigDecimal.valueOf(pduOutMap.get(7).getElectric()));
                }
                if (pduOutMap.get(7).getPower() != null) {
                    buDataPdu.setPower7(BigDecimal.valueOf(pduOutMap.get(7).getPower()));
                }
                if (pduOutMap.get(7).getOutStatus() != null) {
                    buDataPdu.setStatus7(pduOutMap.get(7).getOutStatus());
                }
            }

            // 通道8
            if (pduOutMap.get(8) != null) {
                if (pduOutMap.get(8).getElectric() != null) {
                    buDataPdu.setElectric8(BigDecimal.valueOf(pduOutMap.get(8).getElectric()));
                }
                if (pduOutMap.get(8).getPower() != null) {
                    buDataPdu.setPower8(BigDecimal.valueOf(pduOutMap.get(8).getPower()));
                }
                if (pduOutMap.get(8).getOutStatus() != null) {
                    buDataPdu.setStatus8(pduOutMap.get(8).getOutStatus());
                }
            }

            IBuDataPduService buDataPduService = SpringUtils.getBean(BuDataPduServiceImpl.class);
            buDataPduService.insertBuDataPdu(buDataPdu);

        }
    }

    /**
     * 发送合并数据到Kafka
     * 将PDU主设备数据和所有通道数据合并为一个JSON消息发送
     */
    private void sendCombinedDataToKafka() {
        if (kafkaService == null) {
            logger.warn("Kafka服务未配置，无法发送PDU数据");
            return;
        }

        if (dataSent) {
            logger.info("本批次数据已发送，不重复发送");
            return;
        }

        try {
            long timestamp = System.currentTimeMillis();
            
            // 创建合并数据的JSON对象
            JSONObject combinedData = new JSONObject();

            // 添加批次号
            String batchCode = generateBatchId(timestamp);
            combinedData.put("batchCode", batchCode);

            // 添加设备基本信息
            combinedData.put("deviceId", device.getId());
            combinedData.put("deviceCode", device.getCode());
            combinedData.put("sn", device.getSn());
            combinedData.put("timestamp", timestamp);

            // 添加PDU主设备数据
            // 更新PDU主设备数据中的批次号
            pduData.setBatchCode(batchCode);
            combinedData.put("pduData", pduData);

            // 添加所有通道数据
            combinedData.put("channelData", channelOutputBuffer);

            // 创建Kafka消息
            KafkaMessage kafkaMessage = new KafkaMessage();
            kafkaMessage.setSn(device.getSn());
            kafkaMessage.setType(KafkaMsgType.PDU_DEVICE.ordinal());
            kafkaMessage.setCode("PDU_COMBINED_DATA_" + device.getCode());
            kafkaMessage.setMsg(combinedData.toJSONString());
            kafkaMessage.setInitialTime(timestamp);

            // 发送到Kafka
            kafkaService.send2Kafka(kafkaMessage);

            logger.info(">>>>>>>>>>>>>>合并发送PDU数据成功! 批次号: {}", batchCode);

            // 标记数据已发送
            dataSent = true;

        } catch (Exception e) {
            logger.error("合并发送PDU数据到Kafka失败", e);
        }
    }

    /**
     * 生成批次号
     * 使用船舶SN+设备ID+时间戳组合生成唯一批次号
     * 确保在多船多采集端环境下批次号全局唯一
     *
     * @param timestamp 时间戳(毫秒)
     * @return 生成的批次号
     */
    private String generateBatchId(long timestamp) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timestamp),
                java.time.ZoneId.systemDefault());
        String timeStr = dateTime.format(formatter);
        String shipSn = this.getSn();

        if (shipSn == null || shipSn.isEmpty()) {
            logger.warn("生成批次号时船舶SN为空，使用默认值'UNKNOWN'");
            shipSn = "UNKNOWN";
        }

        // 船舶SN_设备ID_时间戳
        return shipSn + "_" + device.getId() + "_" + timeStr;
    }

    /**
     * 重置数据收集状态
     * 在开始新一轮查询时调用
     */
    private void resetDataCollectionStatus() {
        dataCollectionComplete = false;
        receivedChannelCount = 0;
        allChannelsCollected = false;
        channelOutputBuffer.clear();
        dataSent = false;

        // 重置原始数据收集器
        rawDataCollection.clear();
        collectionStartTime = 0;
    }

    /**
     * 通知所有监听器接收到新消息
     */
    private void notifyListeners(String messageType, String hexData, Object parsedData) {
        for (DeviceMessageListener listener : listeners) {
            try {
                listener.onMessageReceived(messageType, hexData, parsedData);
            } catch (Exception e) {
                logger.error("通知消息监听器时发生异常", e);
            }
        }
    }
} 