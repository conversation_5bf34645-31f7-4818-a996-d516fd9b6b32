package com.snct.serialport.jserial;

import com.snct.common.config.SnctConfig;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.system.domain.SerialConfig;
import com.snct.system.domain.SysOperLog;
import com.snct.system.service.ISerialConfigService;
import com.snct.system.service.impl.SerialConfigServiceImpl;
import com.snct.system.service.impl.SysOperLogServiceImpl;
import com.snct.utils.SysCmd2;
import com.fazecast.jSerialComm.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * class
 *
 * <AUTHOR>
 */
@Component
public class SerialPortUtil3 {

    private static SysOperLogServiceImpl sysOperLogService = SpringUtils.getBean(SysOperLogServiceImpl.class);

    private static final Logger logger = LoggerFactory.getLogger(SerialPortUtil3.class);

    /**
     * 服务器所有串口
     */
    private static Set<String> serialPortNameSets;

    private static Map<String, SerialPort> openPortMap = new ConcurrentHashMap<>();

    /**
     * 查找电脑上所有可用 com 端口
     *
     * @return 可用端口名称列表，没有时 列表为空
     */
    public static void findSystemAllSerialPort() {
        serialPortNameSets = new HashSet<>();
        SerialPort[] portList = SerialPort.getCommPorts();

        logger.info("===========串口信息start==============");
        for (SerialPort serialPort : portList) {
            StringBuilder returnStr = new StringBuilder();
            String portName = serialPort.getSystemPortName();
            returnStr.append("端口：").append(portName).append("---占用情况:").append(serialPort.isOpen());
            logger.info("---{}", returnStr.toString());
            serialPortNameSets.add(portName);
        }
        logger.info("===========串口信息end==============");
    }

    public static Set<String> getSerialPortNameSets() {
        return serialPortNameSets;
    }

    public static void addOpenPort(String portName, SerialPort serialPort) {
        if (serialPort == null) {
            return;
        }
        openPortMap.put(portName, serialPort);
    }

    /**
     * 删除已打开的串口
     */
    public static void delOpenPort(String portName) {
        if (openPortMap.get(portName) == null) {
            return;
        }
        closeComPort(openPortMap.get(portName));
        openPortMap.remove(portName);
    }

    /**
     * 删除已打开的串口
     */
    public static void delOpenPort(List<String> portNameList) {
        if (portNameList == null) {
            return;
        }
        for (String portName : portNameList) {
            delOpenPort(portName);
        }
    }

    /**
     * 获取已打开的串口
     */
    public static SerialPort getOpenPortByName(String portName) {
        return openPortMap.get(portName);
    }

//    /**
//     * 获取已打开的串口
//     */
//    public static SerialPort getOpenPortByName(String portName) {
//        String oldSerialPort = getOldSerialPort(portName);
//        return openPortMap.get(oldSerialPort);
//    }


    /**
     * 根据新的串口名称获取对应的旧串口名称
     *
     * @param newSerialPort 新的串口名称
     * @return 对应的旧串口名称，如果没有找到则返回null
     */
    public static String getOldSerialPort(String newSerialPort) {
        ISerialConfigService serialConfigService = SpringUtils.getBean(SerialConfigServiceImpl.class);
        if (StringUtils.isNotBlank(newSerialPort)) {
            SerialConfig se = new SerialConfig();
            se.setNewName(newSerialPort);
            SerialConfig serialConfig = serialConfigService.selectSerialConfig(se);
            if (serialConfig != null) {
                String oldSerialName = serialConfig.getOldName();
                return oldSerialName;
            }
            return null;
        }
        return null;
    }

    /**
     * 清除已打开的串口
     */
    public static void cleanOpenMap() {
        if (openPortMap.size() == 0) {
            return;
        }
        for (String key : openPortMap.keySet()) {
            closeComPort(openPortMap.get(key));
        }
        openPortMap.clear();
    }

    /**
     * 打开电脑上指定的串口
     *
     * @param portName 端口名称，如 COM1，为 null 时，默认使用电脑中能用的端口中的第一个
     * @param b        波特率(baudrate)，如 9600
     * @param d        数据位（datebits），如 8
     * @param s        停止位（stopbits），如 1
     * @param p        校验位 (parity)，如 0
     * @return 打开的串口对象，打开失败时，返回 null
     */
    public static SerialPort openComPort(String portName, int b, int d, int s, int p) {
        try {
            logger.info("开始打开串口：portName={},baudrate={},datebits={},stopbits={},parity={}", portName, b, d, s, p);

            // 通过端口名称获取串口对象
            SerialPort serialPort = SerialPort.getCommPort(portName);

            // 设置串口参数
            serialPort.setBaudRate(b);
            serialPort.setNumDataBits(d);
            serialPort.setNumStopBits(s);
            serialPort.setParity(p);

            // 打开串口
            if (serialPort.openPort()) {
                logger.info("打开串口: {} 成功...", portName);
//                SysOperLog sysOperLog = new SysOperLog();
//                sysOperLog.setTitle("打开串口:"+ portName+ "成功");
//                sysOperLogService.insertOperlog(sysOperLog);
                return serialPort;
            } else {
                logger.info("串口: {} 打开失败...", portName);
            }
        } catch (Exception e) {
            logger.error("打开串口异常: {}", e.getMessage());
            e.printStackTrace();
        }
        logger.info("打开串口: {} 失败...", portName);
        SysOperLog sysOperLog = new SysOperLog();
        sysOperLog.setCostTime(0l);
        sysOperLog.setTitle("打开串口:" + portName + "失败");
        sysOperLogService.insertOperlog(sysOperLog);
        return null;
    }

    /**
     * 往串口发送数据
     *
     * @param serialPort 串口对象
     * @param bytes      待发送数据
     */
    public static void sendDataToComPort(SerialPort serialPort, byte[] bytes) {
        if (bytes == null || serialPort == null) {
            return;
        }
        logger.info("串口{}发送#######数据：", serialPort.getSystemPortName());
        try {
            int bytesWritten = serialPort.writeBytes(bytes, bytes.length);
            if (bytesWritten == bytes.length) {
                logger.debug("串口{}数据发送成功", serialPort.getSystemPortName());
            } else {
                logger.warn("串口{}数据发送不完整，期望发送{}字节，实际发送{}字节",
                    serialPort.getSystemPortName(), bytes.length, bytesWritten);
            }
        } catch (Exception e) {
            logger.error("串口{}发送数据失败: {}", serialPort.getSystemPortName(), e.getMessage());
            e.printStackTrace();
        }
    }

    public static void sendDataToComPortList(List<String> portNameList, byte[] bytes) {
        if (portNameList == null) {
            return;
        }
        for (String portName : portNameList) {
            sendDataToComPort(getOpenPortByName(portName), bytes);
        }
    }
    /**
     * 通过系统命令往串口写数据
     * 执行: printf 'data' > /dev/ttyS3
     *
     * @param data 要发送的数据内容
     * @param devicePath 串口设备路径，如 /dev/ttyS3
     * @return 执行结果，true表示成功，false表示失败
     */
    public static boolean sendDataByCommand(String data, String devicePath) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(devicePath)) {
            logger.error("发送数据失败：数据内容或设备路径为空");
            return false;
        }

        try {

            File file = new File(SnctConfig.getDevUrl() +"/send.sh");
            if (file.exists()) {
                file.delete();
            }

            //String data = "这是要保存到文件中的字符串";
            try (FileWriter writer = new FileWriter(SnctConfig.getDevUrl() +"/send.sh")) {
                writer.write("printf '"+data+"\\r\\n' > "+ devicePath);
                //System.out.println("文件写入成功");
            } catch (IOException e) {
                e.printStackTrace();
            }
            Thread.sleep(5000);
            // 使用SysCmd2工具类执行命令
            SysCmd2 sysCmd = SpringUtils.getBean(SysCmd2.class);
            sysCmd.cmd( "chmod +x "+ SnctConfig.getDevUrl() +"/send.sh");
            sysCmd.cmd("sudo "+SnctConfig.getDevUrl() +"/send.sh");
            sysCmd.cmd("bash "+SnctConfig.getDevUrl() +"/send.sh");


//            // 构建命令：printf 'data' > devicePath
//            //String command = String.format("sh -c \"printf '%s\\r\\n' > %s\"", data, devicePath);
//            //logger.info("执行串口写入命令: {}", command);
//            String command = SnctConfig.getDevUrl() +"/send.sh " + devicePath + ' ' + data + "\\r\\n";
//            logger.info("执行串口写入命令: {}", command);
//
//            // 使用SysCmd2工具类执行命令
//            SysCmd2 sysCmd = SpringUtils.getBean(SysCmd2.class);
//            sysCmd.cmd(command);
//
//            logger.info("串口数据发送完成: {} -> {}", data, devicePath);
            return true;

        } catch (Exception e) {
            logger.error("串口数据发送异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通过系统命令往串口写数据（带换行符）
     * 类似于执行: printf 'data\r\n' > /dev/ttyS3
     *
     * @param data 要发送的数据内容
     * @param devicePath 串口设备路径，如 /dev/ttyS3
     * @return 执行结果，true表示成功，false表示失败
     */
    public static boolean sendDataByCommandWithNewline(String data, String devicePath) {
        return sendDataByCommand(data + "\\r\\n", devicePath);
    }

    /**
     * 从串口读取数据
     *
     * @param serialPort 要读取的串口
     * @return 读取的数据
     */
    public static byte[] readData(SerialPort serialPort, Integer wait) {
        byte[] allBytes = null;
        try {
            // 获得数据长度
            int bufflenth = serialPort.bytesAvailable();
            while (bufflenth > 0) {
                // 初始化byte数组
                byte[] bytes = new byte[bufflenth];
                int numRead = serialPort.readBytes(bytes, bufflenth);
                if (numRead > 0) {
                    byte[] actualBytes = new byte[numRead];
                    System.arraycopy(bytes, 0, actualBytes, 0, numRead);
                    allBytes = ArrayUtils.addAll(allBytes, actualBytes);
                }
                Thread.sleep(wait);
                bufflenth = serialPort.bytesAvailable();
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return allBytes;
    }

    /**
     * 给串口设置监听
     *
     * @param serialPort
     * @param listener
     */
    public static void setListenerToSerialPort(SerialPort serialPort, SerialPortDataListener listener) {
        // 添加数据监听器
        serialPort.addDataListener(listener);
        logger.info("监听成功----{}", serialPort.getSystemPortName());
    }


    /**
     * 关闭串口
     *
     * @param serialPort 待关闭的串口对象
     */
    public static void closeComPort(SerialPort serialPort) {
        if (serialPort == null) {
            return;
        }
        try {
            serialPort.removeDataListener();
            serialPort.closePort();
            logger.info("关闭串口 :---{}", serialPort.getSystemPortName());
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("关闭串口:" + serialPort.getSystemPortName());
            sysOperLogService.insertOperlog(sysOperLog);
        } catch (Exception e) {
            logger.info("关闭串口错误:---{}", e);
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setTitle("关闭串口错误");
            sysOperLogService.insertOperlog(sysOperLog);
        }
    }

    /**
     * 关闭串口
     *
     * @param serialPortList 待关闭的串口对象
     */
    public static void closeComPortList(List<SerialPort> serialPortList) {
        if (serialPortList == null) {
            return;
        }
        for (SerialPort serialPort : serialPortList) {
            closeComPort(serialPort);
        }
    }

    /**
     * 16进制字符串转十进制字节数组
     * 这是常用的方法，如某些硬件的通信指令就是提供的16进制字符串，发送时需要转为字节数组再进行发送
     *
     * @param strSource 16进制字符串，如 "455A432F5600"，每两位对应字节数组中的一个10进制元素
     *                  默认会去除参数字符串中的空格，所以参数 "45 5A 43 2F 56 00" 也是可以的
     * @return 十进制字节数组, 如 [69, 90, 67, 47, 86, 0]
     */
    public static byte[] hexString2Bytes(String strSource) {
        if (strSource == null || "".equals(strSource.trim())) {
            logger.info("hexString2Bytes 参数为空，放弃转换.");
            return null;
        }
        strSource = strSource.replace(" ", "");
        int l = strSource.length() / 2;
        byte[] ret = new byte[l];
        for (int i = 0; i < l; i++) {
            ret[i] = Integer.valueOf(strSource.substring(i * 2, i * 2 + 2), 16).byteValue();
        }
        return ret;
    }


    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2)
            data[i / 2] =
                    (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
        return data;
    }

    public static void sendCmd(String cmd, SerialPort serialPort) {
        sendDataToComPort(serialPort, hexStringToByteArray(cmd));
    }
}
